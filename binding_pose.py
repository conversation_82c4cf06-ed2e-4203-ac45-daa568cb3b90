import os
import numpy as np
import pandas as pd
import lmdb
from biopandas.pdb import PandasPdb
from rdkit import Chem
from rdkit.Chem import AllChem
from sklearn.cluster import KMeans
from rdkit.Chem.rdMolAlign import AlignMolConformers
from unimol.utils.docking_utils import docking_data_pre, ensemble_iterations
from tqdm import tqdm
import pickle
import re
import json
import copy

CASF_PATH = "./data/protein_ligand_binding_pose_prediction/CASF-2016"
main_atoms = ["N", "CA", "C", "O", "H"]


def rmsd_func(holo_coords, predict_coords):
    """Calculate RMSD between predicted and true coordinates"""
    if predict_coords is not np.nan:
        sz = holo_coords.shape
        rmsd = np.sqrt(np.sum((predict_coords - holo_coords)**2) / sz[0])
        return rmsd
    return 1000.0


def print_result(rmsd_results):
    """Print RMSD statistics and table"""
    print('*'*100)
    print(f'results length: {len(rmsd_results)}')
    print('RMSD < 0.5 : ', np.mean(rmsd_results<0.5))
    print('RMSD < 1.0 : ', np.mean(rmsd_results<1.0))
    print('RMSD < 1.5 : ', np.mean(rmsd_results<1.5))
    print('RMSD < 2.0 : ', np.mean(rmsd_results<2.0))
    print('RMSD < 3.0 : ', np.mean(rmsd_results<3.0))
    print('RMSD < 5.0 : ', np.mean(rmsd_results<5.0))
    print('avg RMSD : ', np.mean(rmsd_results))

    # Print table format
    print('\nTable format:')
    print('1.0 Å ↑: {:.4f}'.format(np.mean(rmsd_results<1.0)))
    print('1.5 Å ↑: {:.4f}'.format(np.mean(rmsd_results<1.5)))
    print('2.0 Å ↑: {:.4f}'.format(np.mean(rmsd_results<2.0)))
    print('3.0 Å ↑: {:.4f}'.format(np.mean(rmsd_results<3.0)))
    print('5.0 Å ↑: {:.4f}'.format(np.mean(rmsd_results<5.0)))


def run_inference_for_pdb(pdb_id, data_path="./CASF-2016", results_path="./results/",
                         weight_path="/content/binding_pose_220908.pt", seed=42):
    """Run inference for a single PDB ID"""
    try:
        # Load original ligand SMILES
        supp = Chem.SDMolSupplier(os.path.join(CASF_PATH, "casf2016", pdb_id + "_ligand.sdf"))
        mol = [mol for mol in supp if mol][0]
        ori_smiles = Chem.MolToSmiles(mol)
        smiles_list = [ori_smiles]

        # Write LMDB
        write_lmdb(pdb_id, smiles_list, seed=seed, result_dir=data_path)

        # Run inference
        batch_size = 8
        dist_threshold = 8.0
        recycling = 3

        cmd = f"python ./unimol/infer.py --user-dir ./unimol {data_path} --valid-subset {pdb_id} --results-path {results_path} --num-workers 8 --ddp-backend=c10d --batch-size {batch_size} --task docking_pose --loss docking_pose --arch docking_pose --path {weight_path} --fp16 --fp16-init-scale 4 --fp16-scale-window 256 --dist-threshold {dist_threshold} --recycling {recycling} --log-interval 50 --log-format simple"

        result = os.system(cmd)
        if result != 0:
            print(f"Inference failed for {pdb_id}")
            return None

        # Generate docking input
        predict_file = os.path.join(results_path, "content_" + pdb_id + ".out.pkl")
        reference_file = os.path.join(data_path, pdb_id + ".lmdb")

        if not os.path.exists(predict_file):
            print(f"Prediction file not found for {pdb_id}: {predict_file}")
            return None

        generate_docking_input(predict_file, reference_file, tta_times=10, output_dir=results_path)

        # Generate final coordinates
        for i in range(len(smiles_list)):
            input_path = os.path.join(results_path, "{}.{}.pkl".format(pdb_id, i))
            ligand_path = os.path.join(results_path, "docking.{}.{}.sdf".format(pdb_id, i))
            cmd = "python ./unimol/utils/coordinate_model.py --input {} --output-ligand {}".format(
                input_path, ligand_path
            )
            os.system(cmd)

        # Calculate RMSD
        return calculate_rmsd_for_pdb(pdb_id, results_path)

    except Exception as e:
        print(f"Error processing {pdb_id}: {str(e)}")
        return None


def calculate_rmsd_for_pdb(pdb_id, results_path):
    """Calculate RMSD for a single PDB"""
    try:
        # Load true ligand coordinates
        true_ligand_path = os.path.join(CASF_PATH, "casf2016", pdb_id + "_ligand.sdf")
        true_supp = Chem.SDMolSupplier(true_ligand_path)
        true_mol = [mol for mol in true_supp if mol][0]
        true_mol = Chem.RemoveHs(true_mol)
        true_coords = true_mol.GetConformer().GetPositions().astype(np.float32)

        # Load predicted ligand coordinates
        pred_ligand_path = os.path.join(results_path, f"docking.{pdb_id}.0.sdf")
        if not os.path.exists(pred_ligand_path):
            print(f"Predicted ligand file not found: {pred_ligand_path}")
            return None

        pred_mol = Chem.MolFromMolFile(pred_ligand_path, sanitize=False)
        if pred_mol is None:
            print(f"Failed to load predicted molecule for {pdb_id}")
            return None

        pred_coords = pred_mol.GetConformer().GetPositions().astype(np.float32)

        # Calculate RMSD
        rmsd = rmsd_func(true_coords, pred_coords)
        print(f"PDB {pdb_id}: RMSD = {rmsd:.3f}")
        return rmsd

    except Exception as e:
        print(f"Error calculating RMSD for {pdb_id}: {str(e)}")
        return None


def load_from_CASF(pdb_id):
    try:
        pdb_path = os.path.join(CASF_PATH, "casf2016", pdb_id + "_protein.pdb")
        pmol = PandasPdb().read_pdb(pdb_path)
        pocket_residues = json.load(
            open(os.path.join(CASF_PATH, "casf2016.pocket.json"))
        )[pdb_id]
        return pmol, pocket_residues
    except:
        print("Currently not support parsing pdb and pocket info from local files.")


def normalize_atoms(atom):
    return re.sub("\d+", "", atom)


def single_conf_gen(tgt_mol, num_confs=1000, seed=42, removeHs=True):
    mol = copy.deepcopy(tgt_mol)
    mol = Chem.AddHs(mol)
    allconformers = AllChem.EmbedMultipleConfs(
        mol, numConfs=num_confs, randomSeed=seed, clearConfs=True
    )
    sz = len(allconformers)
    for i in range(sz):
        try:
            AllChem.MMFFOptimizeMolecule(mol, confId=i)
        except:
            continue
    if removeHs:
        mol = Chem.RemoveHs(mol)
    return mol


def clustering_coords(mol, M=1000, N=100, seed=42, removeHs=True):
    rdkit_coords_list = []
    rdkit_mol = single_conf_gen(mol, num_confs=M, seed=seed, removeHs=removeHs)
    noHsIds = [
        rdkit_mol.GetAtoms()[i].GetIdx()
        for i in range(len(rdkit_mol.GetAtoms()))
        if rdkit_mol.GetAtoms()[i].GetAtomicNum() != 1
    ]
    ### exclude hydrogens for aligning
    AlignMolConformers(rdkit_mol, atomIds=noHsIds)
    sz = len(rdkit_mol.GetConformers())
    for i in range(sz):
        _coords = rdkit_mol.GetConformers()[i].GetPositions().astype(np.float32)
        rdkit_coords_list.append(_coords)

    ### exclude hydrogens for clustering
    rdkit_coords_flatten = np.array(rdkit_coords_list)[:, noHsIds].reshape(sz, -1)
    ids = (
        KMeans(n_clusters=N, random_state=seed)
        .fit_predict(rdkit_coords_flatten)
        .tolist()
    )
    coords_list = [rdkit_coords_list[ids.index(i)] for i in range(N)]
    return coords_list


def parser(pdb_id, smiles, seed=42):
    pmol, pocket_residues = load_from_CASF(pdb_id)
    pname = pdb_id
    pro_atom = pmol.df["ATOM"]
    pro_hetatm = pmol.df["HETATM"]

    pro_atom["ID"] = pro_atom["chain_id"].astype(str) + pro_atom[
        "residue_number"
    ].astype(str)
    pro_hetatm["ID"] = pro_hetatm["chain_id"].astype(str) + pro_hetatm[
        "residue_number"
    ].astype(str)

    pocket = pd.concat(
        [
            pro_atom[pro_atom["ID"].isin(pocket_residues)],
            pro_hetatm[pro_hetatm["ID"].isin(pocket_residues)],
        ],
        axis=0,
        ignore_index=True,
    )

    pocket["normalize_atom"] = pocket["atom_name"].map(normalize_atoms)
    pocket = pocket[pocket["normalize_atom"] != ""]
    patoms = pocket["atom_name"].apply(normalize_atoms).values.tolist()
    # Fix: Create numpy array properly and convert to list for pickling
    pcoords_array = np.array(pocket[["x_coord", "y_coord", "z_coord"]].values, dtype=np.float32)
    pcoords = [pcoords_array.copy()]
    side = [0 if a in main_atoms else 1 for a in patoms]
    residues = (
        pocket["chain_id"].astype(str) + pocket["residue_number"].astype(str)
    ).values.tolist()

    # generate ligand conformation
    M, N = 100, 10
    mol = Chem.MolFromSmiles(smiles)
    mol = Chem.AddHs(mol)
    AllChem.EmbedMolecule(mol, randomSeed=seed)
    latoms = [atom.GetSymbol() for atom in mol.GetAtoms()]
    # Fix: Create numpy array properly
    holo_coords_array = np.array(mol.GetConformer().GetPositions(), dtype=np.float32)
    holo_coordinates = [holo_coords_array.copy()]
    holo_mol = mol
    coordinate_list = clustering_coords(mol, M=M, N=N, seed=seed, removeHs=False)
    # Fix: Ensure coordinate_list contains proper numpy arrays
    coordinate_list = [np.array(coords, dtype=np.float32).copy() for coords in coordinate_list]
    mol_list = [mol] * N

    return pickle.dumps(
        {
            "atoms": latoms,
            "coordinates": coordinate_list,
            "mol_list": mol_list,
            "pocket_atoms": patoms,
            "pocket_coordinates": pcoords,
            "side": side,
            "residue": residues,
            "holo_coordinates": holo_coordinates,
            "holo_mol": holo_mol,
            "holo_pocket_coordinates": pcoords,
            "smi": smiles,
            "pocket": pname,
        },
        protocol=pickle.HIGHEST_PROTOCOL,
    )


def write_lmdb(pdb_id, smiles_list, seed=42, result_dir="./results"):
    os.makedirs(result_dir, exist_ok=True)
    outputfilename = os.path.join(result_dir, pdb_id + ".lmdb")
    try:
        os.remove(outputfilename)
    except:
        pass
    env_new = lmdb.open(
        outputfilename,
        subdir=False,
        readonly=False,
        lock=False,
        readahead=False,
        meminit=False,
        max_readers=1,
        map_size=int(10e9),
    )
    for i, smiles in enumerate(smiles_list):
        inner_output = parser(pdb_id, smiles, seed=seed)
        txn_write = env_new.begin(write=True)
        txn_write.put(f"{i}".encode("ascii"), inner_output)
    txn_write.commit()
    env_new.close()


def generate_docking_input(
    predict_file, reference_file, tta_times=10, output_dir="./results"
):
    (
        mol_list,
        smi_list,
        pocket_list,
        pocket_coords_list,
        distance_predict_list,
        holo_distance_predict_list,
        holo_coords_list,
        holo_center_coords_list,
    ) = docking_data_pre(reference_file, predict_file)
    iter = ensemble_iterations(
        mol_list,
        smi_list,
        pocket_list,
        pocket_coords_list,
        distance_predict_list,
        holo_distance_predict_list,
        holo_coords_list,
        holo_center_coords_list,
        tta_times=tta_times,
    )
    for i, content in enumerate(iter):
        pocket = content[3]
        output_name = os.path.join(output_dir, "{}.{}.pkl".format(pocket, i))
        try:
            os.remove(output_name)
        except:
            pass
        pd.to_pickle(content, output_name)


def main():
    """Main function to run inference on all CASF-2016 data"""
    # Get all PDB IDs from CASF-2016 dataset
    casf_collect = os.listdir(os.path.join(CASF_PATH, "casf2016"))
    casf_collect = list(set([item[:4] for item in casf_collect if item.endswith('_ligand.sdf')]))
    casf_collect.sort()

    print(f"Found {len(casf_collect)} PDB entries in CASF-2016 dataset")
    print("PDB IDs:", casf_collect[:10], "..." if len(casf_collect) > 10 else "")

    # Configuration
    seed = 42
    data_path = "./data/protein_ligand_binding_pose_prediction/CASF-2016"
    results_path = "./results/binding_pose"
    weight_path = "./results/docking/checkpoint_best.pt"

    # Create results directory
    os.makedirs(results_path, exist_ok=True)

    # Collect RMSD results
    rmsd_results = []
    failed_pdbs = []

    print("Starting inference on all CASF-2016 data...")
    for i, pdb_id in enumerate(tqdm(casf_collect)):
        print(f"\nProcessing {i+1}/{len(casf_collect)}: {pdb_id}")

        rmsd = run_inference_for_pdb(pdb_id, data_path, results_path, weight_path, seed)

        if rmsd is not None:
            rmsd_results.append(rmsd)
        else:
            failed_pdbs.append(pdb_id)

        # Clean up intermediate files to save space
        try:
            # Remove LMDB files
            lmdb_file = os.path.join(data_path, pdb_id + ".lmdb")
            if os.path.exists(lmdb_file):
                os.remove(lmdb_file)

            # Remove intermediate pickle files
            for pkl_file in [f"{pdb_id}.0.pkl", f"content_{pdb_id}.out.pkl"]:
                pkl_path = os.path.join(results_path, pkl_file)
                if os.path.exists(pkl_path):
                    os.remove(pkl_path)
        except:
            pass

    # Convert to numpy array and print results
    rmsd_results = np.array(rmsd_results)

    print(f"\n{'='*100}")
    print(f"FINAL RESULTS")
    print(f"{'='*100}")
    print(f"Successfully processed: {len(rmsd_results)}/{len(casf_collect)} PDB entries")
    print(f"Failed PDB entries: {len(failed_pdbs)}")
    if failed_pdbs:
        print(f"Failed PDBs: {failed_pdbs}")

    if len(rmsd_results) > 0:
        print_result(rmsd_results)
    else:
        print("No successful predictions to analyze!")


if __name__ == "__main__":
    main()
