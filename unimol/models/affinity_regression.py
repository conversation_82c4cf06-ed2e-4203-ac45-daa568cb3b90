import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
from unicore.models import BaseUnicoreModel, register_model, register_model_architecture
from .transformer_encoder_with_pair import TransformerEncoderWithPair
from unicore.modules import TransformerEncoderLayer
from unicore.modules import LayerNorm, init_bert_params
from .unimol import base_architecture
from unicore import utils
from .torchmd_etf2d import TorchMD_ETF2D
from .output_modules import EquivariantScalar, <PERSON>alar
try:
    import esm
except ImportError:
    esm = None
import numpy as np

logger = logging.getLogger(__name__)

@register_model("affinity_regres")
class AffinityRegresModel(BaseUnicoreModel):
    @staticmethod
    def add_args(parser):
        """Add model-specific arguments to the parser."""
        parser.add_argument(
            "--aff_finetune",
            type=int,
            default=0,
            help='1: activate,0: deactivate',
        )
        parser.add_argument(
            "--LBA_data",
            type=str,
            default="/home/<USER>/limh/LBADATA/lba",
            help='',
        )
        parser.add_argument(
            "--LBA_random_data",
            type=str,
            default=None,
            help='',
        )
        parser.add_argument(
            "--DUDE_data",
            type=str,
            default=None,
            help='',
        )
        parser.add_argument(
            "--DUDE_fold",
            type=int,
            default=1,
            help='',
        )
        parser.add_argument(
            "--LEP_data",
            type=str,
            default=None,
            help='',
        )
        parser.add_argument(
            "--complex-pretrained-model",
            type=str,
            default='',
            help=''
        ) # use complex pretrained model
        parser.add_argument(
            "--lig-pretrained",
            type=str,
            default='',
            help=''
        ) # use ligand pretrained model
        parser.add_argument(
            "--proc-pretrained",
            type=str,
            default='',
            help=''
        ) # use protein pretrained model
        parser.add_argument(
            "--proc-freeze",
            type=int,
            default=1,
            help=''
        ) # freeze the pocket encoder
        parser.add_argument(
            "--ligand-freeze",
            type=int,
            default=1,
            help=''
        ) # freeze the ligand encoder
        parser.add_argument(
            "--x-norm-loss",
            type=float,
            metavar="D",
            help="x norm loss ratio",
        )
        parser.add_argument(
            "--delta-pair-repr-norm-loss",
            type=float,
            metavar="D",
            help="delta encoder pair repr norm loss ratio",
        )
        parser.add_argument(
            "--idx-path",
            type=str,
            default=None,
            help='',
        )
        parser.add_argument(
            "--max-comnum",
            type=int,
            default=None,
            help='',
        )
        parser.add_argument(
            "--net",
            choices = ['complex_crnet', 'old_transformer'],
            default='complex_crnet',
            help='Use cross atten like unimol or old transformer'
        )
        parser.add_argument(
            "--recycling",
            type=int,
            default=3,
            help="recycling nums of decoder(unimol)",
        )
        parser.add_argument(
            "--CLS-use",
            choices = ['complex_CLS', 'seperate_CLS'],
            default='complex_CLS',
            help='Input which kind of CLS to regression head'
        )
        parser.add_argument(
            "--complex-layernum",
            type=int,
            default=3,
            help='The transformer layer when use tranformer to concat protein ligand information',
        )
        parser.add_argument(
            "--freeze-pretrained-transformer",
            type=int,
            default=0,
            help='if freeze the pretrained transformer',
        )
        parser.add_argument(
            "--mode",
            type=str,
            default="train",
            choices=["train", "infer"],
        )

        parser.add_argument(
            "--use-frad",
            type=int,
            default=0,
            help='use frad as ligand encoder'
        )

        parser.add_argument(
            "--use-esm",
            type=int,
            default=0,
            help='use esmfold as protein encoder'
        )

        parser.add_argument(
            "--use-BindNet",
            type=int,
            default=1,
            help='use BindNet to fuse information'
        )

        parser.add_argument(
            "--random-choose-residue",
            type=int,
            default=0,
            help='random choose residue if residue num + ligand num > max_atom_num'
        )

        parser.add_argument(
            "--extract-pairwise-feat",
            type=int,
            default=0,
            help=''
        )
        parser.add_argument(
            "--extract-feat",
            type=int,
            default=0,
            help='extract ligand and pocket atom embeddings'
        )

    def __init__(self, args, dictionary, lig_dictionary=None):
        super().__init__()
        affinity_regres_architecture(args)
        self.args = args
        self.padding_idx = dictionary.pad()    # record the padding token
        self._num_updates = None
        K = 128

        # Protein Encoder
        if hasattr(self.args, 'use_esm') and self.args.use_esm:
            self.encoder = esm.pretrained.esmfold_v1()
            self.freeze_params(self.encoder)
            self.protein_dimension_reduction = NonLinearHead(1024, 512, args.activation_fn)
            self.protein_pair_dimension_reduction = NonLinearHead(128, 64, args.activation_fn)
        else:
            self.embed_tokens = nn.Embedding(
                len(dictionary), args.encoder_embed_dim, self.padding_idx
            )
            self.encoder = TransformerEncoderWithPair(
                encoder_layers=args.encoder_layers,
                embed_dim=args.encoder_embed_dim,
                ffn_embed_dim=args.encoder_ffn_embed_dim,
                attention_heads=args.encoder_attention_heads,
                emb_dropout=args.emb_dropout,
                dropout=args.dropout,
                attention_dropout=args.attention_dropout,
                activation_dropout=args.activation_dropout,
                max_seq_len=args.max_seq_len,
                activation_fn=args.activation_fn,
                no_final_head_layer_norm=args.delta_pair_repr_norm_loss < 0,
            )
            n_edge_type = len(dictionary) * len(dictionary)
            self.gbf_proj = NonLinearHead(
                K, args.encoder_attention_heads, args.activation_fn
            )
            self.gbf = GaussianLayer(K, n_edge_type)

        # Ligand Encoder
        if hasattr(self.args, 'use_frad') and self.args.use_frad:
            # use frad as mol encoder
            shared_args ={'hidden_channels': 256, 'num_layers': 8, 'num_rbf': 64, 'rbf_type': 'expnorm', 'trainable_rbf': False, 'activation': 'silu', 'neighbor_embedding': True, 'cutoff_lower': 0.0, 'cutoff_upper': 5.0, 'max_z': 100, 'max_num_neighbors': 32}
            self.lig_encoder = TorchMD_ETF2D(
                attn_activation="silu",
                num_heads=8,
                distance_influence="both",
                layernorm_on_vec="whitened",
                md17=False,
                seperate_noise=False,
                **shared_args
            )
            hidden_channels = shared_args['hidden_channels']
            self.proj_head = EquivariantScalar(hidden_channels, hidden_channels*2) # change 256 --> 512
            self.pairwise_head = Scalar(hidden_channels * 4, output_dims=64) # pair wise embeddings transfer
        else:
            self.lig_embed_tokens = nn.Embedding(
                len(lig_dictionary), args.encoder_embed_dim, self.padding_idx
            )
            self.lig_encoder = TransformerEncoderWithPair(
                encoder_layers=args.encoder_layers,
                embed_dim=args.encoder_embed_dim,
                ffn_embed_dim=args.encoder_ffn_embed_dim,
                attention_heads=args.encoder_attention_heads,
                emb_dropout=args.emb_dropout,
                dropout=args.dropout,
                attention_dropout=args.attention_dropout,
                activation_dropout=args.activation_dropout,
                max_seq_len=args.max_seq_len,
                activation_fn=args.activation_fn,
                no_final_head_layer_norm=args.delta_pair_repr_norm_loss < 0,
            )
            lig_n_edge_type = len(lig_dictionary) * len(lig_dictionary)
            self.lig_gbf_proj = NonLinearHead(
            K, args.encoder_attention_heads, args.activation_fn
            )
            self.lig_gbf = GaussianLayer(K, lig_n_edge_type)

        # Transformer
        if hasattr(self.args, 'use_BindNet') and self.args.use_BindNet:
            if self.args.net == 'complex_crnet': # like unimol
                self.concat_decoder = TransformerEncoderWithPair(
                    encoder_layers=4,
                    embed_dim=args.encoder_embed_dim,
                    ffn_embed_dim=args.encoder_ffn_embed_dim,
                    attention_heads=args.encoder_attention_heads,
                    emb_dropout=0.1,
                    dropout=0.1,
                    attention_dropout=0.1,
                    activation_dropout=0.0,
                    activation_fn="gelu",
                )

            elif self.args.net == 'old_transformer':
                self.complex_layernum = args.complex_layernum
                self.complex_layers = nn.ModuleList(
                    [
                        TransformerEncoderLayer(
                            embed_dim=args.encoder_embed_dim,
                            ffn_embed_dim=args.encoder_ffn_embed_dim,
                            attention_heads=args.encoder_attention_heads,
                            dropout=args.emb_dropout,
                            attention_dropout=args.dropout,
                            activation_dropout=args.attention_dropout,
                            activation_fn=args.activation_fn,
                            post_ln=False,
                        )
                        for _ in range(args.complex_layernum)
                    ]
                )
            else:
                raise KeyError("choose net from complex_crnet and old_transformer")



        # Classification Head
        if self.args.CLS_use == "complex_CLS":
            self.aff_regression_head = NonLinearHead(args.encoder_embed_dim, 1, args.activation_fn)
        elif self.args.CLS_use == "seperate_CLS":
            self.aff_regression_head = NonLinearHead(args.encoder_embed_dim * 2, 1, args.activation_fn)
        else:
            raise KeyError("choose CLS_use from complex_CLS and seperate_CLS")


        # Load Model
        self.apply(init_bert_params)
        # If use BindNet, load the whole model
        if hasattr(self.args, 'use_BindNet') and self.args.use_BindNet:
            self.load_complex_retrained_model(self.args.complex_pretrained_model)
            if self.args.freeze_pretrained_transformer > 0:
                self.freeze_params(self.concat_decoder)
        # If not, just load two encoder
        else:
            self.load_ligand_pretrained_model(self.args.lig_pretrained)
            if not (hasattr(self.args, 'use_esm') and self.args.use_esm):
                self.load_pocket_pretrained_model(self.args.complex_pretrained_model)




    @classmethod
    def build_model(cls, args, task):
        """Build a new model instance."""
        return cls(args, task.dictionary, task.lig_dictionary)

    def load_complex_retrained_model(self, complex_pretrained):
        logger.info(f"Loading complex pretraind weight from {complex_pretrained}")
        complex_state_dict = torch.load(complex_pretrained, map_location=lambda storage, loc: storage)
        missing_keys, not_matched_keys = self.load_state_dict(complex_state_dict['model'], strict=False)
        logging.info(f'loadding complex model weight, missing_keys is {missing_keys}\n, not_matched_keys is {not_matched_keys}\n')
        # freeze weight
        self.freeze_params(self.embed_tokens)
        self.freeze_params(self.gbf_proj)
        self.freeze_params(self.gbf)
        self.freeze_params(self.encoder)
        self.freeze_params(self.lig_embed_tokens)
        self.freeze_params(self.lig_gbf_proj)
        self.freeze_params(self.lig_gbf)
        self.freeze_params(self.lig_encoder)


    def load_pocket_pretrained_model(self, poc_pretrained):
        logger.info(f"Loading pocket pretraind weight from {poc_pretrained}")
        poc_state_dict = torch.load(poc_pretrained, map_location=lambda storage, loc: storage)
        missing_keys, not_matched_keys = self.load_state_dict(poc_state_dict['model'], strict=False)
        logging.info(f'loadding pocket model weight, missing_keys is {missing_keys}\n, not_matched_keys is {not_matched_keys}\n')
        filter_lig_keys = []
        for k in missing_keys:
            if not k.startswith('lig_'):
                filter_lig_keys.append(k)
        logging.info(f'loadding pocket model weight, filter lig weight missing_keys is {filter_lig_keys}\n')
        # freeze weight
        if self.args.proc_freeze:
            self.freeze_params(self.embed_tokens)
            self.freeze_params(self.gbf_proj)
            self.freeze_params(self.gbf)
            self.freeze_params(self.encoder)

    def load_ligand_pretrained_model(self, lig_pretrained):
        # load model parameter manually
        logger.info("Loading ligand pretrained weights from {}".format(lig_pretrained))
        state_dict = torch.load(lig_pretrained, map_location=lambda storage, loc: storage)
        # load weight by hand
        if hasattr(self.args, 'use_frad') and self.args.use_frad:
            new_state_dict = {}
            for k, v in state_dict['state_dict'].items():
                if 'model.representation_model' in k:
                    new_k = k.replace('model.representation_model.', '')
                    new_state_dict[new_k] = v
            missing_keys, not_matched_keys = self.lig_encoder.load_state_dict(new_state_dict, strict=False)
            logging.info(f'loadding lig model weight, missing_keys is {missing_keys}\n, not_matched_keys is {not_matched_keys}\n')
            self.freeze_params(self.lig_encoder)
            return
        token_weight_dict = {'weight': state_dict['model']['embed_tokens.weight']}
        self.lig_embed_tokens.load_state_dict(token_weight_dict, strict=True)
        gbf_proj_weight_dict = {'linear1.weight': state_dict['model']['gbf_proj.linear1.weight'], 'linear1.bias': state_dict['model']['gbf_proj.linear1.bias'], 'linear2.weight': state_dict['model']['gbf_proj.linear2.weight'], 'linear2.bias' : state_dict['model']['gbf_proj.linear2.bias']}
        self.lig_gbf_proj.load_state_dict(gbf_proj_weight_dict, strict=True)
        gbf_weight_dict = {'means.weight': state_dict['model']['gbf.means.weight'], 'stds.weight': state_dict['model']['gbf.stds.weight'], 'mul.weight': state_dict['model']['gbf.mul.weight'], 'bias.weight': state_dict['model']['gbf.bias.weight']}
        self.lig_gbf.load_state_dict(gbf_weight_dict, strict=True)
        model_dict = {k.replace('encoder.',''):v for k, v in state_dict['model'].items()}
        missing_keys, not_matched_keys = self.lig_encoder.load_state_dict(model_dict, strict=False)
        logging.info(f'loadding lig model weight, missing_keys is {missing_keys}\n, not_matched_keys is {not_matched_keys}\n')
        # NOTE todo fix lig_encoder, fix and freeze
        # freeze weight
        if self.args.ligand_freeze:
            self.freeze_params(self.lig_embed_tokens)
            self.freeze_params(self.lig_gbf_proj)
            self.freeze_params(self.lig_gbf)
            self.freeze_params(self.lig_encoder)

    def freeze_params(self, model):
        for param in model.parameters():
            param.requires_grad = False

    def check_lig_eval(self):
        if self.lig_encoder.training:
            self.lig_encoder.eval()
        if hasattr(self.args, 'use_frad') and self.args.use_frad:
            return
        if self.lig_embed_tokens.training:
            self.lig_embed_tokens.eval()
        if self.lig_gbf_proj.training:
            self.lig_gbf_proj.eval()
        if self.lig_gbf.training:
            self.lig_gbf.eval()
        if self.lig_encoder.training:
            self.lig_encoder.eval()


    def check_pocket_eval(self):
        if self.embed_tokens.training:
            self.embed_tokens.eval()
        if self.gbf_proj.training:
            self.gbf_proj.eval()
        if self.gbf.training:
            self.gbf.eval()
        if self.encoder.training:
            self.encoder.eval()


    def forward(
        self,
        src_tokens=None,
        src_distance=None,
        src_coord=None,
        src_edge_type=None,
        encoder_masked_tokens=None,
        features_only=False,
        classification_head_name=None,
        mol_graph=None,
        lig_feat_input=None,
        lig_num_lst=None,
        prot_num_lst=None,
        # for molecular lig part
        lig_tokens=None,
        lig_distance=None,
        lig_coord=None,
        lig_edge_type=None,
        frad_dataset=None,
        aa_sequence=None,
        protein_aa_num=None,
        protein_aa_num_eachLigand = None,
        **kwargs
    ):
        #print(src_tokens.shape)
        if frad_dataset is not None:
            frad_dataset.to(src_tokens.device)

        # If freeze the ligand encoder or pocket encoder
        if self.args.ligand_freeze:
            self.check_lig_eval()
        if self.args.proc_freeze:
            if hasattr(self.args, 'use_esm') and self.args.use_esm:
                self.encoder = self.encoder.eval()
            else:
                self.check_pocket_eval()


        # Define the function to get the Gaussian distance feature
        def get_dist_features(dist, et):
            n_node = dist.size(-1)
            gbf_feature = self.gbf(dist, et)
            gbf_result = self.gbf_proj(gbf_feature)
            graph_attn_bias = gbf_result
            graph_attn_bias = graph_attn_bias.permute(0, 3, 1, 2).contiguous()
            graph_attn_bias = graph_attn_bias.view(-1, n_node, n_node)
            return graph_attn_bias
        def get_dist_feature_lig(dist, et):
            n_node = dist.size(-1)
            gbf_feature = self.lig_gbf(dist, et)
            gbf_result = self.lig_gbf_proj(gbf_feature)
            graph_attn_bias = gbf_result
            graph_attn_bias = graph_attn_bias.permute(0, 3, 1, 2).contiguous()
            graph_attn_bias = graph_attn_bias.view(-1, n_node, n_node)
            return graph_attn_bias


        # Protein Encoder Calculation
        with torch.no_grad():
            if hasattr(self.args, 'use_esm') and self.args.use_esm:
                assert aa_sequence != None                    # protein_sequence为列表, 每个元素为 aa sequence
                output = self.encoder.infer(aa_sequence)      # output为esmfold结果字典, 在esmfold原代码中更改了recycle次数和block数目
                encoder_rep = output['s_s']                        # batchSize * maxSeqLen * latentDimension(1024)
                encoder_pair_rep = output['s_z']                   # batchSize * maxSeqLen * maxSeqLen * latentDimension(128)
                padding_mask = output["chain_index"]
                for param in self.protein_dimension_reduction.parameters():
                    device = param.device
                    dtype = param.dtype
                    break
                prot_padding_mask = padding_mask
                prot_padding_mask = prot_padding_mask.to(device)
                # 提取真正是残基的条目, 并重新组装回矩阵
                batchsize = len(protein_aa_num_eachLigand)                     # batch size
                embed_dim = encoder_rep.shape[-1]                              # intent shape
                pair_embed_dim = encoder_pair_rep.shape[-1]                    # pair intent shape

                max_res_num = max([sum([int(x) for x in i.split(',')]) for i in protein_aa_num_eachLigand])
                encoder_rep_true = torch.zeros(batchsize, max_res_num, embed_dim, device=device, dtype=dtype)              # zero encoder_rep
                encoder_pair_rep_true = torch.zeros(batchsize, max_res_num, max_res_num, pair_embed_dim, device=device, dtype=dtype)    # zero_encoder_pair_rep
                prot_padding_mask_true = torch.ones(batchsize, max_res_num, device=device, dtype=dtype)             # idx mask

                for i in range(batchsize): # batchsize
                    chain_index_mask = output["chain_index"][i]
                    chain_index_mask[chain_index_mask!=-1] = True
                    chain_index_mask[chain_index_mask==-1] = False
                    chain_index_mask = list(map(bool, chain_index_mask))
                    protein_aa_num_eachLigand_mask = list(map(bool, [int(x) for x in protein_aa_num_eachLigand[i].split(',')]))
                    tmp_encoder_rep = encoder_rep[i][chain_index_mask]
                    tmp_encoder_rep = tmp_encoder_rep[protein_aa_num_eachLigand_mask]
                    tmp_encoder_pair_rep = encoder_pair_rep[i][chain_index_mask][:, chain_index_mask]
                    tmp_encoder_pair_rep = tmp_encoder_pair_rep[protein_aa_num_eachLigand_mask][:, protein_aa_num_eachLigand_mask]
                    encoder_rep_true[i, :tmp_encoder_rep.shape[0]] = tmp_encoder_rep
                    encoder_pair_rep_true[i, :tmp_encoder_pair_rep.shape[0], :tmp_encoder_pair_rep.shape[1]] = tmp_encoder_pair_rep
                    prot_padding_mask_true[i, :tmp_encoder_rep.shape[0]] = 0
                encoder_rep = encoder_rep_true

                # print(encoder_rep.device, encoder_rep.dtype)

                encoder_rep = self.protein_dimension_reduction(encoder_rep)                 # Convert protein embedding dimension from 1024 to 512
                encoder_pair_rep = encoder_pair_rep_true
                encoder_pair_rep = self.protein_pair_dimension_reduction(encoder_pair_rep)  # Convert pair embedding from 128 to 64
                encoder_pair_rep[encoder_pair_rep == float("-inf")] = 0
                prot_padding_mask = prot_padding_mask_true
            else:
                prot_padding_mask = src_tokens.eq(self.padding_idx)    # Recond the padding token
                if self.args.proc_freeze:
                    with torch.no_grad():
                        x = self.embed_tokens(src_tokens)
                        graph_attn_bias = get_dist_features(src_distance, src_edge_type)
                        (
                            encoder_rep,
                            encoder_pair_rep,
                            delta_encoder_pair_rep,
                            x_norm,
                            delta_encoder_pair_rep_norm,
                        ) = self.encoder(x, padding_mask=prot_padding_mask, attn_mask=graph_attn_bias)
                else:
                    x = self.embed_tokens(src_tokens)
                    graph_attn_bias = get_dist_features(src_distance, src_edge_type)
                    (
                        encoder_rep,
                        encoder_pair_rep,
                        delta_encoder_pair_rep,
                        x_norm,
                        delta_encoder_pair_rep_norm,
                    ) = self.encoder(x, padding_mask=prot_padding_mask, attn_mask=graph_attn_bias)
                encoder_pair_rep[encoder_pair_rep == float("-inf")] = 0



        # Ligand Encoder Calculation
        lig_padding_mask = lig_tokens.eq(self.padding_idx)
        if self.args.ligand_freeze:
            with torch.no_grad():
                if hasattr(self.args, 'use_frad') and self.args.use_frad:
                    # Check data type from encoder_rep instead of undefined x
                    if '16' in str(encoder_rep.dtype):
                        frad_dataset.pos = frad_dataset.pos.half()
                    xnew, vec, z, pos, batch = self.lig_encoder(frad_dataset.z, frad_dataset.pos, frad_dataset.batch)
                    # 256 --> 512 node embedding, special head
                    xnew_proj = self.proj_head.pre_reduce(xnew, vec) # update dims
                    x_feat_lst = []
                    batch_size = batch.max().item() + 1
                    feat_len_lst = []
                    for i in range(batch_size):
                        # insert a cls embedding, take mean of all
                        mol_embs = xnew_proj[batch==i]
                        mean_embs = mol_embs.mean(dim=0).reshape(1, -1)
                        mol_embs_final = torch.concat((mean_embs, mol_embs))
                        feat_len_lst.append(mol_embs_final.shape[0])
                        x_feat_lst.append(mol_embs_final)
                    # padding to the same length
                    max_len = max(feat_len_lst)
                    embed_size = xnew_proj.shape[1]
                    lig_encoder_rep = torch.zeros((batch_size, max_len, embed_size), device=xnew.device, dtype=xnew.dtype)
                    lig_padding_mask = torch.ones((batch_size, max_len), device=xnew.device, dtype=torch.bool)
                    for i in range(batch_size):
                        feat_len = feat_len_lst[i]
                        lig_encoder_rep[i, :feat_len, :] = x_feat_lst[i]
                        lig_padding_mask[i,:feat_len] = False
                    # pairwise embedding
                    # 512 --> concat???
                    expand_lig_a = lig_encoder_rep.unsqueeze(2)
                    expand_lig_b = lig_encoder_rep.unsqueeze(1)
                    repeat_lig_a = expand_lig_a.expand(batch_size, max_len, max_len, embed_size)
                    repeat_lig_b = expand_lig_b.expand(batch_size, max_len, max_len, embed_size)
                    lig_pair_wise = torch.cat((repeat_lig_a, repeat_lig_b), dim=3)
                    lig_encoder_pair_rep = self.pairwise_head.pre_reduce(lig_pair_wise) # get pairwise embedding
                    # Create dummy attention bias for frad case
                    lig_graph_attn_bias = torch.zeros(batch_size, max_len, max_len, device=xnew.device, dtype=xnew.dtype)
                else:
                    lig_x = self.lig_embed_tokens(lig_tokens)
                    lig_graph_attn_bias = get_dist_feature_lig(lig_distance, lig_edge_type)
                    (
                        lig_encoder_rep,
                        lig_encoder_pair_rep,
                        lig_delta_encoder_pair_rep,
                        lig_x_norm,
                        lig_delta_encoder_pair_rep_norm,
                    ) = self.lig_encoder(lig_x, padding_mask=lig_padding_mask, attn_mask=lig_graph_attn_bias)
        else:
            lig_x = self.lig_embed_tokens(lig_tokens)
            lig_graph_attn_bias = get_dist_feature_lig(lig_distance, lig_edge_type)
            (
                lig_encoder_rep,
                lig_encoder_pair_rep,
                lig_delta_encoder_pair_rep,
                lig_x_norm,
                lig_delta_encoder_pair_rep_norm,
            ) = self.lig_encoder(lig_x, padding_mask=lig_padding_mask, attn_mask=lig_graph_attn_bias)


        # Transformer Calculation
        if hasattr(self.args, 'use_BindNet') and self.args.use_BindNet:
            if self.args.net == 'complex_crnet': # like unimol:
                pocket_encoder_rep = encoder_rep
                pocket_encoder_pair_rep = encoder_pair_rep
                pocket_padding_mask = prot_padding_mask
                mol_encoder_rep = lig_encoder_rep
                mol_graph_attn_bias = lig_graph_attn_bias
                mol_padding_mask = lig_padding_mask
                mol_encoder_pair_rep = lig_encoder_pair_rep
                mol_sz = lig_encoder_rep.size(1)
                pocket_sz = pocket_encoder_rep.size(1)
                concat_rep = torch.cat(
                    [mol_encoder_rep, pocket_encoder_rep], dim=-2
                )  # [batch, mol_sz+pocket_sz, hidden_dim]
                concat_mask = torch.cat(
                    [mol_padding_mask, pocket_padding_mask], dim=-1
                )  # [batch, mol_sz+pocket_sz]
                attn_bs = mol_graph_attn_bias.size(0)

                concat_attn_bias = torch.zeros(
                    attn_bs, mol_sz + pocket_sz, mol_sz + pocket_sz
                ).type_as(
                    concat_rep
                )  # [batch, mol_sz+pocket_sz, mol_sz+pocket_sz]
                concat_attn_bias[:, :mol_sz, :mol_sz] = (
                    mol_encoder_pair_rep.permute(0, 3, 1, 2)
                    .reshape(-1, mol_sz, mol_sz)
                    .contiguous()
                )
                concat_attn_bias[:, -pocket_sz:, -pocket_sz:] = (
                    pocket_encoder_pair_rep.permute(0, 3, 1, 2)
                    .reshape(-1, pocket_sz, pocket_sz)
                    .contiguous()
                )
                decoder_rep = concat_rep
                decoder_pair_rep = concat_attn_bias
                for i in range(self.args.recycling):
                    decoder_outputs = self.concat_decoder(decoder_rep, padding_mask=concat_mask, attn_mask=decoder_pair_rep)
                    decoder_rep = decoder_outputs[0]
                    decoder_pair_rep = decoder_outputs[1]
                    if i != (self.args.recycling - 1):
                        decoder_pair_rep = decoder_pair_rep.permute(0, 3, 1, 2).reshape(
                            -1, mol_sz + pocket_sz, mol_sz + pocket_sz
                        )
                if self.args.extract_pairwise_feat:
                    # # following procedure produces pairwise embedding with shape: [batch_size, pocket_num, lig_num, feature_dim]
                    # # TODO: this will not work when we have batched data
                    # breakpoint()
                    # mol_pocket_pair_decoder_rep = (
                    #     decoder_pair_rep[:, :mol_sz, mol_sz:, :]
                    #     + decoder_pair_rep[:, mol_sz:, :mol_sz, :].transpose(1, 2)
                    # ) / 2.0
                    # mol_pocket_pair_decoder_rep[mol_pocket_pair_decoder_rep == float("-inf")] = 0

                    # mol_pocket_pair_decoder_rep = mol_pocket_pair_decoder_rep.transpose(1,2)
                    # # pair_mask = ~pocket_padding_mask.T*~mol_padding_mask
                    # # pair_mask = pair_mask.unsqueeze(0)
                    # # pair_mask = pair_mask.unsqueeze(-1)
                    # num_true_pocket = torch.sum(~pocket_padding_mask).item()
                    # num_true_mol = torch.sum(~mol_padding_mask).item()
                    
                    # mol_pocket_pair_decoder_rep = mol_pocket_pair_decoder_rep[:, :num_true_pocket, :num_true_mol, :] # remove padding token(start and end token also true)
                    # mol_pocket_pair_decoder_rep = mol_pocket_pair_decoder_rep[:, 1:-1, 1:-1, :] # remove start and end token

                    # following procedure produces pairwise embedding with shape  [batch_size, lig_num + pocket_num, lig_num + pocket_num, feature_dim]
                    #breakpoint()
                    decoder_pair_rep[decoder_pair_rep == float("-inf")] = 0
                    lig_lig_pair_rep = decoder_pair_rep[:, :mol_sz, :mol_sz, :]
                    pocket_pocket_pair_rep = decoder_pair_rep[:, mol_sz:, mol_sz:, :]
                    lig_pocket_pair_rep = decoder_pair_rep[:, :mol_sz, mol_sz:, :]
                    pocket_lig_pair_rep = decoder_pair_rep[:, mol_sz:, :mol_sz, :]
                    #breakpoint()
                    # Handle batch processing correctly - get max valid lengths across batch
                    batch_size = pocket_padding_mask.size(0)
                    max_true_pocket = 0
                    max_true_mol = 0
                    for b in range(batch_size):
                        num_true_pocket_b = torch.sum(~pocket_padding_mask[b]).item()
                        num_true_mol_b = torch.sum(~mol_padding_mask[b]).item()
                        max_true_pocket = max(max_true_pocket, num_true_pocket_b)
                        max_true_mol = max(max_true_mol, num_true_mol_b)

                    lig_lig_pair_rep = lig_lig_pair_rep[:, :max_true_mol, :max_true_mol, :]
                    pocket_pocket_pair_rep = pocket_pocket_pair_rep[:, :max_true_pocket, :max_true_pocket, :]
                    lig_pocket_pair_rep = lig_pocket_pair_rep[:, :max_true_mol, :max_true_pocket, :]
                    pocket_lig_pair_rep = pocket_lig_pair_rep[:, :max_true_pocket, :max_true_mol, :]

                    lig_lig_pair_rep = lig_lig_pair_rep[:, 1:-1, 1:-1, :]
                    pocket_pocket_pair_rep = pocket_pocket_pair_rep[:, 1:-1, 1:-1, :]
                    lig_pocket_pair_rep = lig_pocket_pair_rep[:, 1:-1, 1:-1, :]
                    pocket_lig_pair_rep = pocket_lig_pair_rep[:, 1:-1, 1:-1, :]

                    bz = decoder_pair_rep.shape[0]
                    feature_dim = lig_lig_pair_rep.shape[-1]

                    num_mol = max_true_mol - 2
                    num_pocket = max_true_pocket - 2

                    pairwise_embedding = torch.zeros((bz, num_mol+num_pocket, num_mol+num_pocket, feature_dim),
                                                      device=lig_lig_pair_rep.device)
                    pairwise_embedding[:, :num_mol, :num_mol, :] = lig_lig_pair_rep
                    pairwise_embedding[:, num_mol:, num_mol:, :] = pocket_pocket_pair_rep
                    pairwise_embedding[:, :num_mol, num_mol:, :] = lig_pocket_pair_rep
                    pairwise_embedding[:, num_mol:, :num_mol, :] = pocket_lig_pair_rep
                                        
                    return pairwise_embedding                   
                mol_decoder = decoder_rep[:, :mol_sz]
                pocket_decoder = decoder_rep[:, mol_sz:]
                # Affinity Regression Head Calculation
                if self.args.CLS_use == "complex_CLS":
                    cls_token = decoder_rep[:, 0, :]
                elif self.args.CLS_use == "seperate_CLS":
                    if hasattr(self.args, 'use_esm') and self.args.use_esm:
                        protein_cls_token = pocket_decoder.mean(dim = 1)
                    else:
                        protein_cls_token = pocket_decoder[:, 0, :]
                    ligand_cls_token = mol_decoder[:, 0, :]
                    cls_token = torch.cat([protein_cls_token, ligand_cls_token], dim=-1)
                else:
                    raise KeyError("choose CLS_use from complex_CLS and seperate_CLS")
                bz = mol_decoder.shape[0]
                lig_embedding_lst = []
                pocket_atom_embedding_lst = []
                #breakpoint()
                for i in range(bz):
                    lig_embedding_lst.append(mol_decoder[i][~mol_padding_mask[i]][1:-1]) # remove the start and end token
                    pocket_atom_embedding_lst.append(pocket_decoder[i][~pocket_padding_mask[i]][1:-1]) # remove the start and end token
                # concat the ligand and atom embeddings
                #breakpoint()
                # lig_embedding = torch.cat(lig_embedding_lst, dim=0)
                # pocket_atom_embedding = torch.cat(pocket_atom_embedding_lst, dim=0)
                #breakpoint()                                    
                if self.args.extract_feat:
                    return lig_embedding_lst, pocket_atom_embedding_lst
                affinity_predict = self.aff_regression_head(cls_token).squeeze(-1)
            elif self.args.net == 'old_transformer':
                mol_sz = lig_encoder_rep.size(1)
                pocket_sz = encoder_rep.size(1)
                all_padding_mask = torch.cat([prot_padding_mask, lig_padding_mask], dim=1)
                all_feat_x = torch.cat([encoder_rep, lig_encoder_rep], dim=1)
                for i in range(len(self.complex_layers)):
                    all_feat_x = self.complex_layers[i](
                        all_feat_x, padding_mask=all_padding_mask
                    )
                # Affinity Regression Head Calculation
                if self.args.CLS_use == "complex_CLS":
                    cls_token = all_feat_x[:, 0, :]
                elif self.args.CLS_use == "seperate_CLS":
                    pocket_decoder = all_feat_x[:, mol_sz:]
                    mol_decoder = all_feat_x[:, :mol_sz]
                    protein_cls_token = pocket_decoder[:, 0, :]
                    ligand_cls_token = mol_decoder[:, 0, :]
                    cls_token = torch.cat([protein_cls_token, ligand_cls_token], dim=-1)
                else:
                    raise KeyError("choose CLS_use from complex_CLS and seperate_CLS")
                affinity_predict = self.aff_regression_head(cls_token).squeeze(-1)
            else:
                raise KeyError("choose net from complex_crnet and old_transformer")

        else:
            if hasattr(self.args, 'use_esm') and self.args.use_esm:
                protein_cls_token = encoder_rep.mean(dim = 1)
            else:
                protein_cls_token = encoder_rep[:, 0, :]
            ligand_cls_token = lig_encoder_rep[:, 0, :]
            cls_token = torch.cat([protein_cls_token, ligand_cls_token], dim=-1)
            affinity_predict = self.aff_regression_head(cls_token).squeeze(-1)

        return affinity_predict, None

@torch.jit.script
def gaussian(x, mean, std):
    '''Gaussian function with specific mean and std'''
    pi = 3.14159
    a = (2 * pi) ** 0.5
    return torch.exp(-0.5 * (((x - mean) / std) ** 2)) / (a * std)

class GaussianLayer(nn.Module):
    '''Gaussian layer for distance feature extraction'''
    def __init__(self, K=128, edge_types=1024):
        super().__init__()
        self.K = K
        self.means = nn.Embedding(1, K)
        self.stds = nn.Embedding(1, K)
        self.mul = nn.Embedding(edge_types, 1)
        self.bias = nn.Embedding(edge_types, 1)
        nn.init.uniform_(self.means.weight, 0, 3)
        nn.init.uniform_(self.stds.weight, 0, 3)
        nn.init.constant_(self.bias.weight, 0)
        nn.init.constant_(self.mul.weight, 1)
    def forward(self, x, edge_type):
        mul = self.mul(edge_type).type_as(x)
        bias = self.bias(edge_type).type_as(x)
        x = mul * x.unsqueeze(-1) + bias
        x = x.expand(-1, -1, -1, self.K)
        mean = self.means.weight.float().view(-1)
        std = self.stds.weight.float().view(-1).abs() + 1e-5
        return gaussian(x.float(), mean, std).type_as(self.means.weight)


class NonLinearHead(nn.Module):
    """Head for simple classification or Regression tasks."""
    def __init__(
        self,
        input_dim,
        out_dim,
        activation_fn,
        hidden=None,
    ):
        super().__init__()
        hidden = input_dim if not hidden else hidden
        self.linear1 = nn.Linear(input_dim, hidden)
        self.linear2 = nn.Linear(hidden, out_dim)
        self.activation_fn = utils.get_activation_fn(activation_fn)
    def forward(self, x):
        x = self.linear1(x)
        x = self.activation_fn(x)
        x = self.linear2(x)
        return x


@register_model_architecture("affinity_regres", "affinity_regres")
def affinity_regres_architecture(args):
    args.encoder_layers = getattr(args, "encoder_layers", 15)
    args.encoder_embed_dim = getattr(args, "encoder_embed_dim", 512)
    args.encoder_ffn_embed_dim = getattr(args, "encoder_ffn_embed_dim", 2048)
    args.encoder_attention_heads = getattr(args, "encoder_attention_heads", 64)
    args.dropout = getattr(args, "dropout", 0.1)
    args.emb_dropout = getattr(args, "emb_dropout", 0.1)
    args.attention_dropout = getattr(args, "attention_dropout", 0.1)
    args.activation_dropout = getattr(args, "activation_dropout", 0.0)
    args.pooler_dropout = getattr(args, "pooler_dropout", 0.0)
    args.max_seq_len = getattr(args, "max_seq_len", 512)
    args.activation_fn = getattr(args, "activation_fn", "gelu")
    args.pooler_activation_fn = getattr(args, "pooler_activation_fn", "tanh")
    args.post_ln = getattr(args, "post_ln", False)
    args.masked_coord_loss = getattr(args, "masked_coord_loss", 1.0)
    args.masked_dist_loss = getattr(args, "masked_dist_loss", 1.0)

    base_architecture(args)